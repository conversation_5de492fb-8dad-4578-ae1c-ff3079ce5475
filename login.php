<?php
session_start();

// Database connection
$host = "localhost";
$user = "root"; 
$pass = ""; 
$db = "medical"; 

// Connect sa database
$conn = new mysqli($host, $user, $pass, $db);

// Check connection
if ($conn->connect_error) {
 die("Connection failed: " . $conn->connect_error);
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
 $username = trim($_POST['username']);
 $password = trim($_POST['password']);

    // Prepared statement
 $stmt = $conn->prepare("SELECT * FROM login WHERE username=? AND password=?");
$stmt->bind_param("ss", $username, $password);
 $stmt->execute();
 $result = $stmt->get_result();
    $stmt->close();

 if ($result->num_rows > 0) {
        // ✅ Successful login
 $_SESSION['username'] = $username;
 header("Location: welcome.php");
 exit();
 } else {
        // ❌ Wrong login
$_SESSION['error'] = "Invalid Username or Password"; // Itatala ang error
        
        // IMPORTANT: Mag-redirect pabalik sa index.php
 header("Location: index.php");
 exit();
 }
}

$conn->close();

// Kung direkta pumunta sa login.php, ibalik sa index.php
header("Location: index.php");
exit();
?>