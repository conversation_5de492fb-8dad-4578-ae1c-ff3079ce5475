
<?php
session_start();
$error = isset($_SESSION['error']) ? $_SESSION['error'] : '';
unset($_SESSION['error']); // Clear the error after displaying
?>
<!DOCTYPE html>
<html lang="en">
<head>
 <meta charset="UTF-8">
 <title>Medical Supply Login</title>
 <link rel="stylesheet" href="style.css">
</head>
<body>
 <div class="login-box">
<h2>Login</h2>
 <?php if (!empty($error)): ?>
 <p class="error-msg" style="color: red; font-weight: bold;"><?= htmlspecialchars($error) ?></p>
 <?php endif; ?>
 <form action="login.php" method="POST">
 <input type="text" name="username" placeholder="Username" required>
 <input type="password" name="password" placeholder="Password" required>
<button type="submit">Login</button>
 </form>
 <p>Don't have an account? <a href="#">Sign up</a></p> </div>
</body>
</html>